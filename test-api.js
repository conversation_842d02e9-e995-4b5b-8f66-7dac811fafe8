#!/usr/bin/env node

/**
 * Simple test script to verify API endpoints are working
 * Run with: node test-api.js
 */

const http = require('http');

const BASE_URL = 'http://localhost:3000';

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data,
            error: 'Failed to parse JSON'
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function testEndpoints() {
  console.log('🧪 Testing NPI Website API Endpoints...\n');

  const endpoints = [
    '/api/projects',
    '/api/success-stories',
    '/api/events',
    '/api/news',
    '/api/resources',
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`Testing ${endpoint}...`);
      const result = await makeRequest(endpoint);
      
      if (result.status === 200) {
        console.log(`✅ ${endpoint} - OK (${result.status})`);
        if (result.data && typeof result.data === 'object') {
          if (Array.isArray(result.data)) {
            console.log(`   📊 Returned ${result.data.length} items`);
          } else if (result.data.projects) {
            console.log(`   📊 Returned ${result.data.projects.length} projects`);
          } else if (result.data.docs) {
            console.log(`   📊 Returned ${result.data.docs.length} documents`);
          }
        }
      } else {
        console.log(`❌ ${endpoint} - Error (${result.status})`);
        if (result.error) {
          console.log(`   Error: ${result.error}`);
        }
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - Failed to connect`);
      console.log(`   Error: ${error.message}`);
    }
    console.log('');
  }

  // Test a specific project endpoint if projects exist
  try {
    console.log('Testing project detail endpoint...');
    const projectsResult = await makeRequest('/api/projects');
    
    if (projectsResult.status === 200 && projectsResult.data) {
      let projects = [];
      if (Array.isArray(projectsResult.data)) {
        projects = projectsResult.data;
      } else if (projectsResult.data.projects) {
        projects = projectsResult.data.projects;
      }

      if (projects.length > 0) {
        const firstProject = projects[0];
        const projectId = firstProject.slug || firstProject.id;
        
        console.log(`Testing /api/projects/${projectId}...`);
        const projectResult = await makeRequest(`/api/projects/${projectId}`);
        
        if (projectResult.status === 200) {
          console.log(`✅ Project detail - OK (${projectResult.status})`);
          if (projectResult.data && projectResult.data.project) {
            console.log(`   📄 Project: ${projectResult.data.project.title}`);
          }
        } else {
          console.log(`❌ Project detail - Error (${projectResult.status})`);
        }
      } else {
        console.log('⚠️  No projects found to test detail endpoint');
      }
    }
  } catch (error) {
    console.log(`❌ Project detail test failed: ${error.message}`);
  }

  console.log('\n🏁 API testing completed!');
  console.log('\n💡 To start the development server, run: npm run dev');
}

// Check if server is running
makeRequest('/api/projects')
  .then(() => {
    testEndpoints();
  })
  .catch(() => {
    console.log('❌ Server is not running on http://localhost:3000');
    console.log('💡 Please start the development server first with: npm run dev');
    process.exit(1);
  });
