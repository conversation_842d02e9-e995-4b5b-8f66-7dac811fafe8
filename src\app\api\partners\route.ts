import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const limit = parseInt(searchParams.get('limit') || '10')
    const page = parseInt(searchParams.get('page') || '1')
    const sort = searchParams.get('sort') || '-updatedAt'
    const featured = searchParams.get('featured')
    const type = searchParams.get('type')
    const category = searchParams.get('category')
    const status = searchParams.get('status')

    // Build where clause
    const where: any = {}

    if (featured !== null) {
      where.featured = { equals: featured === 'true' }
    }

    if (type) {
      where.type = { equals: type }
    }

    if (category) {
      where.category = { equals: category }
    }

    if (status) {
      where.status = { equals: status }
    } else {
      // Default to active partners only
      where.status = { equals: 'active' }
    }

    // Fetch partners from CMS
    const result = await payload.find({
      collection: 'partners',
      where,
      limit,
      page,
      sort: [sort],
      select: {
        id: true,
        name: true,
        summary: true,
        description: true,
        logo: true,
        type: true,
        category: true,
        status: true,
        featured: true,
        verified: true,
        contact: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    return NextResponse.json({
      success: true,
      partners: result.docs,
      pagination: {
        totalDocs: result.totalDocs,
        limit: result.limit,
        totalPages: result.totalPages,
        page: result.page,
        pagingCounter: result.pagingCounter,
        hasPrevPage: result.hasPrevPage,
        hasNextPage: result.hasNextPage,
        prevPage: result.prevPage,
        nextPage: result.nextPage,
      },
    })
  } catch (error) {
    console.error('Partners API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch partners',
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // Create new partner
    const result = await payload.create({
      collection: 'partners',
      data: body,
    })

    return NextResponse.json({
      success: true,
      partner: result,
      message: 'Partner created successfully',
    })
  } catch (error) {
    console.error('Partners POST API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to create partner',
      },
      { status: 500 }
    )
  }
}
