#!/usr/bin/env node

/**
 * Seed data via API endpoints
 */

import fetch from 'node-fetch'

const API_BASE = 'http://localhost:3000/api'

// Helper function to create rich text content
const createRichText = (text) => ({
  root: {
    children: [
      {
        children: [
          {
            detail: 0,
            format: 0,
            mode: 'normal',
            style: '',
            text,
            type: 'text',
            version: 1,
          },
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'paragraph',
        version: 1,
      },
    ],
    direction: 'ltr',
    format: '',
    indent: 0,
    type: 'root',
    version: 1,
  },
})

async function seedPartners() {
  console.log('🌱 Seeding Partners via API...')
  
  const partners = [
    {
      name: 'Kenya Vision 2030',
      summary: "Kenya's development blueprint for transforming the country into a middle-income economy.",
      description: createRichText("Kenya's development blueprint for transforming the country into a middle-income economy through sustainable development initiatives."),
      type: 'government',
      category: 'government',
      status: 'active',
      featured: true,
      verified: true,
      contact: {
        website: 'https://vision2030.go.ke',
        primaryContact: {
          name: 'Vision 2030 Secretariat',
          email: '<EMAIL>',
        },
      },
    },
    {
      name: 'National Museums of Kenya',
      summary: 'Leading institution in cultural heritage preservation and traditional knowledge documentation.',
      description: createRichText('Leading institution in cultural heritage preservation and traditional knowledge documentation, supporting NPI in preserving indigenous knowledge systems.'),
      type: 'institution',
      category: 'cultural',
      status: 'active',
      featured: true,
      verified: true,
      contact: {
        website: 'https://museums.or.ke',
        primaryContact: {
          name: 'NMK Partnership Office',
          email: '<EMAIL>',
        },
      },
    },
    {
      name: 'Kenya Association of Manufacturers',
      summary: 'Promoting manufacturing sector development and industrial growth in Kenya.',
      description: createRichText('Promoting manufacturing sector development and industrial growth in Kenya, supporting natural products commercialization and market access.'),
      type: 'private',
      category: 'industry',
      status: 'active',
      featured: false,
      verified: true,
      contact: {
        website: 'https://kam.co.ke',
        primaryContact: {
          name: 'KAM Partnerships',
          email: '<EMAIL>',
        },
      },
    },
    {
      name: 'University of Nairobi',
      summary: 'Leading research institution partnering in natural products research and development.',
      description: createRichText('Leading research institution partnering in natural products research and development, providing scientific validation and research support.'),
      type: 'academic',
      category: 'research',
      status: 'active',
      featured: false,
      verified: true,
      contact: {
        website: 'https://uonbi.ac.ke',
        primaryContact: {
          name: 'UoN Research Office',
          email: '<EMAIL>',
        },
      },
    },
  ]

  for (const partner of partners) {
    try {
      const response = await fetch(`${API_BASE}/partners`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(partner),
      })

      if (response.ok) {
        const result = await response.json()
        console.log(`✅ Created partner: ${partner.name}`)
      } else {
        const error = await response.text()
        console.error(`❌ Failed to create partner ${partner.name}:`, error)
      }
    } catch (error) {
      console.error(`❌ Error creating partner ${partner.name}:`, error.message)
    }
  }
}

async function seedMediaGallery() {
  console.log('🌱 Seeding Media Gallery via API...')
  
  const mediaItems = [
    {
      title: 'NPI Documentary: Transforming Communities',
      description: createRichText('Comprehensive documentary showcasing how NPI initiatives are transforming communities across Kenya through natural products development.'),
      type: 'video',
      category: 'Documentary',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      duration: '12:45',
      views: 15420,
      featured: true,
      publishDate: new Date('2024-01-15').toISOString(),
    },
    {
      title: 'Aloe Vera Cooperative Success Story',
      description: createRichText('Meet the women of Baringo County who transformed their community through traditional aloe vera knowledge.'),
      type: 'video',
      category: 'Success Stories',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      duration: '8:30',
      views: 8750,
      featured: true,
      publishDate: new Date('2023-12-10').toISOString(),
    },
    {
      title: 'Traditional Healers Share Their Wisdom',
      description: createRichText('Interviews with traditional healers from different communities about their knowledge and practices.'),
      type: 'video',
      category: 'Interviews',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      duration: '18:45',
      views: 9800,
      featured: false,
      publishDate: new Date('2023-10-15').toISOString(),
    },
    {
      title: 'Youth Entrepreneurs Leading Change',
      description: createRichText('Feature story on young entrepreneurs who are innovating in the natural products sector.'),
      type: 'video',
      category: 'Youth',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      duration: '10:30',
      views: 6450,
      featured: false,
      publishDate: new Date('2023-09-20').toISOString(),
    },
  ]

  for (const media of mediaItems) {
    try {
      const response = await fetch(`${API_BASE}/media-gallery`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(media),
      })

      if (response.ok) {
        const result = await response.json()
        console.log(`✅ Created media item: ${media.title}`)
      } else {
        const error = await response.text()
        console.error(`❌ Failed to create media item ${media.title}:`, error)
      }
    } catch (error) {
      console.error(`❌ Error creating media item ${media.title}:`, error.message)
    }
  }
}

async function main() {
  console.log('🌱 Starting API-based seeding...')
  
  try {
    await seedPartners()
    await seedMediaGallery()
    
    console.log('🎉 API-based seeding completed!')
    
    // Verify the data was created
    console.log('\n📊 Verification:')
    
    const partnersResponse = await fetch(`${API_BASE}/partners?limit=10`)
    const partnersData = await partnersResponse.json()
    console.log(`Partners: ${partnersData.partners?.length || 0} entries`)
    
    const mediaResponse = await fetch(`${API_BASE}/media-gallery?limit=10`)
    const mediaData = await mediaResponse.json()
    console.log(`Media Gallery: ${mediaData.mediaItems?.length || 0} entries`)
    
  } catch (error) {
    console.error('❌ Seeding failed:', error)
    process.exit(1)
  }
}

main()
