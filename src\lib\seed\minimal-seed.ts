import type { Payload } from 'payload'

export const seedMinimalData = async (payload: Payload): Promise<void> => {
  payload.logger.info('🌱 Starting minimal CMS data seeding...')

  try {
    // Check database connection first
    payload.logger.info('🔍 Checking database connection...')

    // Check if data already exists
    payload.logger.info('🔍 Checking for existing data...')
    const existingCounties = await payload.find({
      collection: 'counties',
      limit: 1,
      pagination: false,
    })

    payload.logger.info(`📊 Found ${existingCounties.docs.length} existing counties`)

    if (existingCounties.docs.length > 0) {
      payload.logger.info('⚠️  Data already exists. Clearing existing data first...')

      // Clear existing data for fresh seeding
      try {
        await payload.delete({ collection: 'counties', where: {} })
        await payload.delete({ collection: 'projects', where: {} })
        await payload.delete({ collection: 'success-stories', where: {} })
        await payload.delete({ collection: 'events', where: {} })
        await payload.delete({ collection: 'media', where: {} })
      } catch (clearError) {
        payload.logger.warn('⚠️  Could not clear some existing data:', clearError)
      }

      payload.logger.info('🗑️  Cleared existing data')
    }

    // Create a simple placeholder image
    const placeholderImageBuffer = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    )

    // Create one media item
    payload.logger.info('📸 Creating media item...')
    const heroImage = await payload.create({
      collection: 'media',
      data: {
        filename: 'hero-image.png',
        mimeType: 'image/png',
        alt: 'Natural products research',
        category: 'general',
        description: 'Hero image for testing',
      },
      file: {
        data: placeholderImageBuffer,
        mimetype: 'image/png',
        name: 'hero-image.png',
        size: placeholderImageBuffer.length,
      },
    })

    payload.logger.info(`✅ Created media item with ID: ${heroImage.id}`)

    // Create a few counties
    const counties = [
      {
        name: 'Baringo',
        code: 'KE-030',
        coordinates: { latitude: 0.4684, longitude: 35.9737 },
        description: 'Known for its rich biodiversity and traditional aloe vera cultivation.',
        isActive: true,
      },
      {
        name: 'Kiambu',
        code: 'KE-022',
        coordinates: { latitude: -1.1719, longitude: 36.8356 },
        description: 'Agricultural hub with strong coffee and tea farming traditions.',
        isActive: true,
      },
    ]

    payload.logger.info('🏞️  Creating counties...')
    const createdCounties = []
    for (const county of counties) {
      payload.logger.info(`Creating county: ${county.name}`)
      const created = await payload.create({
        collection: 'counties',
        data: county,
      })
      createdCounties.push(created)
      payload.logger.info(`✅ Created county: ${county.name} with ID: ${created.id}`)
    }

    payload.logger.info(`✅ Created ${createdCounties.length} counties total`)

    // Create one project
    payload.logger.info('🚀 Creating project...')
    const project = await payload.create({
      collection: 'projects',
      data: {
        title: 'Indigenous Knowledge Documentation Initiative',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'A comprehensive project to document and preserve traditional knowledge systems.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        summary: 'Documenting traditional knowledge systems of indigenous communities.',
        image: {
          image: heroImage.id,
          alt: 'Indigenous Knowledge Documentation',
          caption: 'Community elders sharing knowledge',
        },
        category: 'knowledge-preservation',
        pillar: 'indigenous-knowledge',
        status: 'active',
        location: {
          counties: [createdCounties[0].id],
          specificLocation: 'Baringo County',
          coordinates: {
            latitude: 0.4684,
            longitude: 35.9737,
          },
        },
        timeline: {
          startDate: '2023-01-01',
          endDate: '2025-12-31',
          duration: '3 years',
        },
        budget: {
          totalBudget: 500000,
          currency: 'KES',
          fundingStatus: 'fully-funded',
        },
        impact: {
          beneficiaries: 1000,
          communities: 5,
          description: 'Preserving traditional knowledge for future generations',
        },
        featured: true,
        urgent: false,
        tags: [
          { tag: 'traditional-knowledge' },
          { tag: 'community-engagement' },
        ],
      },
    })

    payload.logger.info(`✅ Created project with ID: ${project.id}`)

    // Create one success story
    payload.logger.info('🌟 Creating success story...')
    const successStory = await payload.create({
      collection: 'success-stories',
      data: {
        title: 'Aloe Vera Cooperative Success',
        summary: 'Local cooperative increases income through sustainable aloe vera processing.',
        content: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'The Chepkemoi Women\'s Cooperative has successfully transformed their community through sustainable aloe vera processing.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        image: {
          image: heroImage.id,
          alt: 'Aloe Vera Processing',
          caption: 'Women processing aloe vera products',
        },
        category: 'economic-empowerment',
        location: {
          county: createdCounties[0].id,
          specificLocation: 'Baringo County',
          coordinates: {
            latitude: 0.4684,
            longitude: 35.9737,
          },
        },
        participants: {
          beneficiary: {
            name: 'Mary Chepkemoi',
            role: 'Cooperative Leader',
            organization: 'Chepkemoi Women\'s Cooperative',
          },
          knowledgeHolder: {
            name: 'Elder Sarah Kiplagat',
            title: 'Traditional Healer',
            expertise: 'Traditional aloe vera processing methods',
          },
        },
        impact: {
          metrics: [
            {
              metric: 'Household Income Increase',
              value: '300',
              unit: 'percent',
              description: 'Average increase in household income',
            },
            {
              metric: 'Jobs Created',
              value: '150',
              unit: 'jobs',
              description: 'New employment opportunities for women',
            },
          ],
          beneficiaries: 150,
          economicImpact: 'Increased household income by 300%',
          socialImpact: 'Created 150 jobs for women',
          environmentalImpact: 'Sustainable harvesting practices implemented',
        },
        testimonials: [
          {
            quote: 'This cooperative has transformed our lives. We now have sustainable income and our children can go to school.',
            author: 'Mary Chepkemoi',
            role: 'Cooperative Leader',
            organization: 'Chepkemoi Women\'s Cooperative',
          },
        ],
        timeline: {
          startDate: '2023-01-01',
          completionDate: '2024-01-15',
          duration: '12 months',
        },
        investment: {
          totalAmount: 500000,
          currency: 'KES',
          sources: [
            {
              source: 'Community Savings',
              amount: 200000,
              percentage: 40,
            },
            {
              source: 'Government Grant',
              amount: 300000,
              percentage: 60,
            },
          ],
        },
        featured: true,
        publishDate: '2024-01-15',
        status: 'published',
      },
    })

    payload.logger.info(`✅ Created success story with ID: ${successStory.id}`)

    // Create one event
    payload.logger.info('📅 Creating event...')
    const event = await payload.create({
      collection: 'events',
      data: {
        title: 'Natural Products Innovation Conference 2024',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'Annual conference bringing together researchers, communities, and industry partners.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        summary: 'Annual conference on natural products innovation and traditional knowledge.',
        image: {
          image: heroImage.id,
          alt: 'Conference participants',
          caption: 'Participants at the innovation conference',
        },
        type: 'conference',
        category: 'knowledge-sharing',
        status: 'upcoming',
        schedule: {
          startDate: '2024-09-15',
          endDate: '2024-09-17',
          startTime: '09:00',
          endTime: '17:00',
        },
        location: {
          venue: 'Kenyatta International Conference Centre',
          address: 'Harambee Avenue, Nairobi',
          county: createdCounties[1].id,
          coordinates: {
            latitude: -1.2921,
            longitude: 36.8219,
          },
        },
        registration: {
          required: true,
          fee: 5000,
          currency: 'KES',
          capacity: 500,
          deadline: '2024-09-01',
        },
        featured: true,
      },
    })

    payload.logger.info(`✅ Created event with ID: ${event.id}`)

    // Verify data was created
    payload.logger.info('🔍 Verifying created data...')
    const finalCounties = await payload.find({ collection: 'counties', limit: 10, pagination: false })
    const finalProjects = await payload.find({ collection: 'projects', limit: 10, pagination: false })
    const finalMedia = await payload.find({ collection: 'media', limit: 10, pagination: false })

    payload.logger.info(`📊 Final verification:`)
    payload.logger.info(`   - Counties: ${finalCounties.docs.length}`)
    payload.logger.info(`   - Projects: ${finalProjects.docs.length}`)
    payload.logger.info(`   - Media: ${finalMedia.docs.length}`)

    payload.logger.info('🎉 Minimal CMS data seeding completed successfully!')

  } catch (error) {
    payload.logger.error('❌ Error during minimal seeding:', error)
    payload.logger.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    })
    throw error
  }
}
