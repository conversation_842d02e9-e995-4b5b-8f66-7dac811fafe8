'use client'

import React, { useState, useEffect } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPI<PERSON>ard,
  NPICardHeader,
  NPICardTitle,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import Image from 'next/image'
import { MapPin, Calendar, Users, TrendingUp, Quote, ArrowRight, Filter, Heart } from 'lucide-react'

interface SuccessStory {
  id: string
  title: string
  summary: string
  fullStory: string
  image: string
  location: string
  county: string
  category: string
  date: string
  impact: {
    metric: string
    value: string
  }
  testimonial: {
    quote: string
    author: string
    role: string
    image?: string
  }
  tags: string[]
  featured: boolean
}

interface NPISuccessStoriesGridProps {
  title?: string
  description?: string
  stories?: SuccessStory[]
}

export const NPISuccessStoriesGridBlock: React.FC<NPISuccessStoriesGridProps> = ({
  title = 'Transforming Lives Across Kenya',
  description = 'From rural villages to urban centers, discover how traditional knowledge and community innovation are creating lasting change.',
}) => {
  const [stories, setStories] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchSuccessStories = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch('/api/success-stories?limit=6')

        if (!response.ok) {
          throw new Error(`Failed to fetch success stories: ${response.status}`)
        }

        const data = await response.json()

        if (data.success && data.stories) {
          setStories(data.stories)
        } else {
          throw new Error('Invalid response format')
        }
      } catch (err) {
        console.error('Error fetching success stories:', err)
        setError(err instanceof Error ? err.message : 'Failed to load success stories')
        // Fallback to empty array
        setStories([])
      } finally {
        setLoading(false)
      }
    }

    fetchSuccessStories()
  }, [])

  // Helper function to get image URL
  const getImageUrl = (story: any): string => {
    if (story.image?.image?.url) {
      return story.image.image.url
    }
    // Fallback to a default image
    return '/assets/product 1.jpg'
  }

  // Helper function to get image alt text
  const getImageAlt = (story: any): string => {
    return story.image?.alt || story.image?.image?.alt || story.title
  }

  // Helper function to format location
  const getLocation = (story: any): string => {
    if (story.location?.specificLocation) {
      return story.location.specificLocation
    }
    if (story.location?.county?.name) {
      return story.location.county.name
    }
    return 'Kenya'
  }

  // Helper function to extract text from rich text content
  const extractTextFromContent = (content: any): string => {
    if (!content?.root?.children) return ''

    const extractText = (node: any): string => {
      if (node.type === 'text') {
        return node.text || ''
      }
      if (node.children) {
        return node.children.map(extractText).join('')
      }
      return ''
    }

    return content.root.children.map(extractText).join(' ').slice(0, 200) + '...'
  }

  if (loading) {
    return (
      <NPISection className="bg-white">
        <NPISectionHeader>
          <NPISectionTitle className="text-black">{title}</NPISectionTitle>
          <NPISectionDescription className="text-[#725242]">{description}</NPISectionDescription>
        </NPISectionHeader>
        <div className="flex justify-center items-center py-12">
          <div className="text-[#725242]">Loading success stories...</div>
        </div>
      </NPISection>
    )
  }

  if (error) {
    return (
      <NPISection className="bg-white">
        <NPISectionHeader>
          <NPISectionTitle className="text-black">{title}</NPISectionTitle>
          <NPISectionDescription className="text-[#725242]">{description}</NPISectionDescription>
        </NPISectionHeader>
        <div className="flex justify-center items-center py-12">
          <div className="text-red-600">Error: {error}</div>
        </div>
      </NPISection>
    )
  }

  const [selectedCategory, setSelectedCategory] = useState('All Categories')
  const [selectedCounty, setSelectedCounty] = useState('All Counties')

  // Create categories and counties from CMS data
  const categories = ['All Categories', ...Array.from(new Set(stories.map((s) => s.category || 'General')))]
  const counties = ['All Counties', ...Array.from(new Set(stories.map((s) => getLocation(s))))]

  const filteredStories = stories.filter((story) => {
    const storyCategory = story.category || 'General'
    const storyCounty = getLocation(story)

    return (
      (selectedCategory === 'All Categories' || storyCategory === selectedCategory) &&
      (selectedCounty === 'All Counties' || storyCounty === selectedCounty)
    )
  })

  const featuredStory = stories.find((story) => story.featured)
  const regularStories = filteredStories.filter((story) => !story.featured)

  return (
    <NPISection className="bg-white">
      <NPISectionHeader>
        <NPISectionTitle className="text-black">{title}</NPISectionTitle>
        <NPISectionDescription className="text-[#725242]">{description}</NPISectionDescription>
      </NPISectionHeader>

      {/* Filters */}
      <NPICard className="mb-8 bg-[#EFE3BA] border-[#8A3E25]">
        <NPICardContent className="p-6">
          <div className="flex items-center gap-4 mb-4">
            <Filter className="w-5 h-5 text-[#8A3E25]" />
            <span className="font-medium font-npi text-black">Filter Stories:</span>
          </div>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2 font-npi text-black">Category</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full p-2 border-2 border-[#725242] focus:outline-none focus:ring-2 focus:ring-[#25718A] font-npi text-[#725242] bg-white"
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 font-npi text-black">County</label>
              <select
                value={selectedCounty}
                onChange={(e) => setSelectedCounty(e.target.value)}
                className="w-full p-2 border-2 border-[#725242] focus:outline-none focus:ring-2 focus:ring-[#25718A] font-npi text-[#725242] bg-white"
              >
                {counties.map((county) => (
                  <option key={county} value={county}>
                    {county}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </NPICardContent>
      </NPICard>

      {/* Featured Story */}
      {featuredStory &&
        selectedCategory === 'All Categories' &&
        selectedCounty === 'All Counties' && (
          <div className="mb-12">
            <h3 className="text-2xl font-bold mb-6 font-npi flex items-center gap-2 text-black">
              <Heart className="w-6 h-6 text-[#8A3E25]" />
              Featured Story
            </h3>
            <NPICard className="overflow-hidden hover:shadow-xl transition-all duration-300 bg-white border-2 border-[#8A3E25]">
              <div className="grid lg:grid-cols-2 gap-0">
                <div className="relative h-64 lg:h-auto">
                  <Image
                    src={getImageUrl(featuredStory)}
                    alt={getImageAlt(featuredStory)}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-[#8A3E25] text-white px-3 py-1 text-sm font-medium">
                      Featured
                    </span>
                  </div>
                </div>

                <div className="p-8">
                  <div className="flex items-center gap-4 text-sm text-[#725242] mb-4">
                    <span className="flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      {getLocation(featuredStory)}
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {featuredStory.timeline?.completionDate ?
                        new Date(featuredStory.timeline.completionDate).toLocaleDateString() :
                        'Ongoing'
                      }
                    </span>
                  </div>

                  <NPICardTitle className="text-2xl mb-4 text-black">{featuredStory.title}</NPICardTitle>

                  <p className="text-[#725242] mb-6 leading-relaxed font-npi">
                    {featuredStory.summary}
                  </p>

                  {featuredStory.impact?.metrics && featuredStory.impact.metrics.length > 0 && (
                    <div className="bg-[#EFE3BA] p-4 mb-6 border-l-4 border-[#8A3E25]">
                      <div className="flex items-center gap-2 mb-2">
                        <TrendingUp className="w-5 h-5 text-[#8A3E25]" />
                        <span className="font-semibold text-[#8A3E25] font-npi">
                          {featuredStory.impact.metrics[0].metric}
                        </span>
                      </div>
                      <div className="text-2xl font-bold text-black font-npi">
                        {featuredStory.impact.metrics[0].value} {featuredStory.impact.metrics[0].unit}
                      </div>
                    </div>
                  )}

                  {featuredStory.testimonials && featuredStory.testimonials.length > 0 && (
                    <blockquote className="border-l-4 border-[#25718A] pl-4 mb-6">
                      <Quote className="w-5 h-5 text-[#25718A] mb-2" />
                      <p className="italic text-[#725242] mb-2 font-npi">
                        &ldquo;{featuredStory.testimonials[0].quote}&rdquo;
                      </p>
                      <footer className="text-sm">
                        <strong className="font-npi text-black">{featuredStory.testimonials[0].author}</strong>
                        <span className="text-[#725242] font-npi">
                          , {featuredStory.testimonials[0].role}
                        </span>
                      </footer>
                    </blockquote>
                  )}

                  <NPIButton asChild className="bg-[#25718A] hover:bg-[#8A3E25] text-white">
                    <Link href={`/success-stories/${featuredStory.slug || featuredStory.id}`}>
                      Read Full Story <ArrowRight className="w-4 h-4 ml-2" />
                    </Link>
                  </NPIButton>
                </div>
              </div>
            </NPICard>
          </div>
        )}

      {/* Stories Grid - Square Cards */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {regularStories.map((story, index) => (
          <NPICard
            key={story.id}
            className={`overflow-hidden hover:shadow-xl transition-all duration-300 h-full flex flex-col border-2 ${
              index % 2 === 0
                ? 'bg-white border-[#8A3E25] hover:border-[#25718A]'
                : 'bg-[#EFE3BA] border-[#725242] hover:border-[#8A3E25]'
            }`}
          >
            {/* Square aspect ratio container */}
            <div className="relative w-full aspect-square flex-shrink-0">
              <Image src={getImageUrl(story)} alt={getImageAlt(story)} fill className="object-cover" />
              <div className="absolute inset-0 bg-black/40" />
              <div className="absolute top-3 left-3">
                <span className="bg-[#8A3E25] text-white px-2 py-1 text-xs font-medium">
                  {story.category || 'General'}
                </span>
              </div>
              <div className="absolute bottom-3 left-3 right-3">
                <div className="text-white text-sm font-medium font-npi">{getLocation(story)}</div>
              </div>
            </div>

            <div className="flex flex-col flex-grow p-4">
              <NPICardHeader className="pb-2 p-0">
                <NPICardTitle className={`text-base leading-tight line-clamp-2 mb-2 ${
                  index % 2 === 0 ? 'text-black' : 'text-black'
                }`}>
                  {story.title}
                </NPICardTitle>
                <div className="flex flex-col gap-1 text-xs">
                  <span className="flex items-center gap-1 text-[#725242]">
                    <Calendar className="w-3 h-3" />
                    {story.timeline?.completionDate ?
                      new Date(story.timeline.completionDate).toLocaleDateString() :
                      'Ongoing'
                    }
                  </span>
                  {story.impact?.metrics && story.impact.metrics.length > 0 && (
                    <span className="text-[#8A3E25] font-medium font-npi text-xs">
                      {story.impact.metrics[0].metric}: {story.impact.metrics[0].value} {story.impact.metrics[0].unit}
                    </span>
                  )}
                </div>
              </NPICardHeader>

              <NPICardContent className="flex-grow pb-2 p-0">
                <p className="text-[#725242] leading-relaxed mb-2 font-npi text-sm line-clamp-2">
                  {story.summary}
                </p>

                {story.testimonials && story.testimonials.length > 0 && (
                  <blockquote className="border-l-2 border-[#25718A] pl-2 italic text-xs mb-2">
                    <p className="mb-1 font-npi line-clamp-1 text-[#725242]">
                      &ldquo;{story.testimonials[0].quote.substring(0, 60)}...&rdquo;
                    </p>
                    <footer className="text-[#725242] font-npi text-xs">
                      <strong>{story.testimonials[0].author}</strong>
                    </footer>
                  </blockquote>
                )}
              </NPICardContent>

              <NPICardFooter className="mt-auto pt-2 p-0">
                <NPIButton
                  asChild
                  className="w-full bg-[#25718A] hover:bg-[#8A3E25] text-white text-xs"
                >
                  <Link href={`/success-stories/${story.slug || story.id}`}>
                    Read Story <ArrowRight className="w-3 h-3 ml-1" />
                  </Link>
                </NPIButton>
              </NPICardFooter>
            </div>
          </NPICard>
        ))}
      </div>

      {/* Results Summary */}
      <div className="text-center mt-8">
        <p className="text-[#725242] font-npi">
          Showing {filteredStories.length} of {stories.length} success stories
        </p>
      </div>
    </NPISection>
  )
}
